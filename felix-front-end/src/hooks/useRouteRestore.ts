import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

const ROUTE_STORAGE_KEY = 'felix_last_route';

/**
 * Hook to preserve and restore route on page refresh
 * This helps maintain the user's position in multi-step flows when refreshing the page
 */
export function useRouteRestore() {
  const navigate = useNavigate();
  const location = useLocation();

  // Save current route to localStorage when it changes
  useEffect(() => {
    // Only save routes that should be restored (use case routes)
    if (location.pathname.includes('/dashboard/use-case/')) {
      localStorage.setItem(ROUTE_STORAGE_KEY, location.pathname);
    }
  }, [location.pathname]);

  // Check for saved route on initial load
  useEffect(() => {
    const savedRoute = localStorage.getItem(ROUTE_STORAGE_KEY);
    
    // If we're at the root route and there's a saved route, navigate to it
    if (
      location.pathname === '/' && 
      savedRoute && 
      savedRoute.includes('/dashboard/use-case/')
    ) {
      console.log(`[RouteRestore] Restoring route: ${savedRoute}`);
      navigate(savedRoute, { replace: true });
    }
  }, [navigate, location.pathname]);
}

export default useRouteRestore; 