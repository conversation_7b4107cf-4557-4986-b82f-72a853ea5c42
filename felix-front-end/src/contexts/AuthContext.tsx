import { createContext, useState, useContext, useCallback, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useIsAuthenticated as useMsalIsAuthenticated } from '@azure/msal-react';
import { UserProfile, login as msalLogin, logout as msalLogout, getActiveAccount } from '../auth/authService';

interface AuthContextType {
  user: UserProfile | null;
  isAuthenticated: boolean;
  isLoginModalOpen: boolean;
  openLoginModal: () => void;
  closeLoginModal: () => void;
  login: () => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const navigate = useNavigate();
  const isMsalAuthenticated = useMsalIsAuthenticated();

  // Check if user is already authenticated
  useEffect(() => {
    const checkAuth = async () => {
      if (isMsalAuthenticated) {
        try {
          const account = await getActiveAccount();
          if (account) {
            // Create user profile from account info
            const userProfile: UserProfile = {
              id: account.localAccountId,
              name: account.name || 'Unknown User',
              email: account.username,
              avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(account.name || 'User')}&background=1A3A4A&color=fff`,
              role: 'User' // Default role, can be updated based on claims or backend response
            };
            setUser(userProfile);
          }
        } catch (error) {
          console.error('Error checking authentication:', error);
        }
      }
    };

    checkAuth();
  }, [isMsalAuthenticated]);

  const isAuthenticated = useMemo(() => !!user, [user]);

  const openLoginModal = useCallback(() => {
    // Only open the modal if the user is not already authenticated
    if (!isAuthenticated) {
      setIsLoginModalOpen(true);
    }
  }, [isAuthenticated]);

  const closeLoginModal = useCallback(() => {
    setIsLoginModalOpen(false);
  }, []);

  const login = useCallback(async () => {
    try {
      const userProfile = await msalLogin();
      setUser(userProfile);
      closeLoginModal();
      navigate('/dashboard');
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }, [closeLoginModal, navigate]);

  const logout = useCallback(async () => {
    try {
      await msalLogout();
      setUser(null);
      navigate('/');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  }, [navigate]);

  const value = useMemo(() => ({
    user,
    isAuthenticated,
    isLoginModalOpen,
    openLoginModal,
    closeLoginModal,
    login,
    logout
  }), [user, isAuthenticated, isLoginModalOpen, openLoginModal, closeLoginModal, login, logout]);

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};