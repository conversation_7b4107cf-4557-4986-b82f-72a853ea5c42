import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

interface AuthGuardProps {
  children: React.ReactNode;
}

/**
 * AuthGuard component to protect routes that require authentication.
 * Redirects to home page and shows login modal if user is not authenticated.
 */
export default function AuthGuard({ children }: AuthGuardProps) {
  const { isAuthenticated, openLoginModal } = useAuth();
  const navigate = useNavigate();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    // Check authentication status
    const checkAuth = async () => {
      if (!isAuthenticated) {
        // Redirect to home page
        navigate('/', { replace: true });
        
        // Show login modal after redirect
        setTimeout(() => {
          openLoginModal();
          setIsChecking(false);
        }, 100);
      } else {
        setIsChecking(false);
      }
    };
    
    checkAuth();
  }, [isAuthenticated, navigate, openLoginModal]);

  // Show loading indicator while checking auth status
  if (isChecking) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Render children only if authenticated
  return isAuthenticated ? <>{children}</> : null;
}
